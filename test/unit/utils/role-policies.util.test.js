import { beforeEach, describe, expect, it, vi } from 'vitest';

import { roleError } from '#src/modules/user/errors/role.error.js';
import { findByIdWithModulePolicies } from '#src/modules/user/repository/role.repository.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import {
  checkPolicies,
  fetchRolePolicies,
  generateRolePoliciesCacheKey,
} from '#src/utils/role-policies.util.js';

vi.mock('#src/utils/cache.util.js', () => ({
  fetchFromCache: vi.fn(),
  generateCacheKey: vi.fn(),
}));

vi.mock('#src/modules/user/repository/role.repository.js', () => ({
  findByIdWithModulePolicies: vi.fn(),
}));

vi.mock('#src/modules/user/errors/role.error.js', () => ({
  roleError: {
    unauthorisedAccess: vi.fn(() => new Error('Unauthorised access')),
    updateNotAllowedForHierarchy: vi.fn().mockImplementation(() => {
      throw new Error('error.roles.sentence.updateNotAllowedForHierarchy');
    }),
    invalidHierarchy: vi.fn().mockImplementation(() => {
      throw new Error('error.roles.sentence.invalidHierarchy');
    }),
    parentHasNoPoliciesAccess: vi.fn().mockImplementation(() => {
      throw new Error('error.roles.sentence.parentHasNoPoliciesAccess');
    }),
    exceedsParentPolicies: vi.fn().mockImplementation(() => {
      throw new Error('error.roles.sentence.exceedsParentPolicies');
    }),
    unsupportedModulePolicies: vi.fn().mockImplementation(() => {
      throw new Error('error.roles.sentence.unsupportedModulePolicies');
    }),
    noEntityAccess: vi.fn().mockImplementation(() => {
      throw new Error('error.roles.sentence.noEntityAccess');
    }),
    noRolePolicyAccess: vi.fn().mockImplementation(() => {
      throw new Error('error.roles.sentence.noRolePolicyAccess');
    }),
    noHierarchyModuleAccess: vi.fn().mockImplementation(() => {
      throw new Error('error.roles.sentence.noHierarchyModuleAccess');
    }),
    moduleAccessDenied: vi.fn().mockImplementation(() => {
      throw new Error('error.roles.sentence.moduleAccessDenied');
    }),
    actionNotAllowed: vi.fn().mockImplementation(() => {
      throw new Error('error.roles.sentence.actionNotAllowed');
    }),
  },
}));

vi.mock('#src/modules/core/constants/core.constant.js', () => ({
  ACCESS_LEVEL_KEYS: {
    DEFAULT: 'default',
    ROOT: 'root',
    MERCHANT: 'merchant',
  },
  MODULE_NAMES: {
    ACCESS_CONTROL: 'accessControls',
    AUDIT_TRAIL: 'auditTrails',
    BULK_JOB: 'bulkJobs',
    CORE: 'core',
    DEPARTMENT_TEMPLATE: 'departmentTemplates',
    DEPARTMENT: 'departments',
    DEVELOPER_HUB: 'developerHubs',
    LOCALISATION: 'localisations',
    ROLE: 'roles',
    SETTING: 'settings',
  },
  CACHE_SECOND: {
    DAILY: 86400,
  },
}));

describe('Role Policies Utility', () => {
  let mockFastify;
  let mockRequest;
  let mockRouteConfig;
  let mockRoleWithPolicies;

  beforeEach(() => {
    vi.resetAllMocks();

    mockFastify = {
      redis: {},
    };

    mockRequest = {
      authInfo: {
        roleId: 'role-123',
      },
      userEntity: {
        id: 'user-entity-123',
        hierarchy: 'root',
      },
      entity: {
        id: 'entity-123',
        hierarchy: 'organisation',
      },
      parentEntity: {
        id: 'parent-entity-123',
      },
      raw: {
        url: '/original-url',
      },
    };

    mockRouteConfig = {
      policy: 'user.read',
    };

    mockRoleWithPolicies = {
      id: 'role-123',
      name: 'Admin',
      modules: {
        organisation: [
          {
            name: 'user',
            policies: {
              read: true,
              write: true,
            },
          },
        ],
        merchant: [
          {
            name: 'user',
            policies: {
              read: true,
            },
          },
        ],
      },
    };

    generateCacheKey.mockReturnValue('cache-key-123');
    fetchFromCache.mockImplementation((_, __, fetchFn) => fetchFn());
    findByIdWithModulePolicies.mockResolvedValue(mockRoleWithPolicies);
  });

  describe('generateRolePoliciesCacheKey', () => {
    it('should generate cache key with modified request URL', () => {
      const roleId = 'test-role-456';
      const expectedModifiedRequest = {
        ...mockRequest,
        raw: {
          ...mockRequest.raw,
          url: `/role-policies/${roleId}`,
        },
      };

      generateRolePoliciesCacheKey(mockRequest, roleId);

      expect(generateCacheKey).toHaveBeenCalledWith('role_policies', expectedModifiedRequest);
    });

    it('should preserve original request properties except URL', () => {
      const roleId = 'test-role-789';

      generateRolePoliciesCacheKey(mockRequest, roleId);

      const callArgs = generateCacheKey.mock.calls[0];
      const modifiedRequest = callArgs[1];

      expect(modifiedRequest.authInfo).toEqual(mockRequest.authInfo);
      expect(modifiedRequest.userEntity).toEqual(mockRequest.userEntity);
      expect(modifiedRequest.entity).toEqual(mockRequest.entity);
      expect(modifiedRequest.parentEntity).toEqual(mockRequest.parentEntity);
      expect(modifiedRequest.raw.url).toBe(`/role-policies/${roleId}`);
    });

    it('should return the value from generateCacheKey', () => {
      const expectedCacheKey = 'generated-cache-key-123';
      generateCacheKey.mockReturnValue(expectedCacheKey);

      const result = generateRolePoliciesCacheKey(mockRequest, 'role-id');

      expect(result).toBe(expectedCacheKey);
    });
  });

  describe('fetchRolePolicies', () => {
    it('should call fetchFromCache with correct parameters', async () => {
      const roleId = 'test-role-123';
      const userEntityId = 'user-entity-456';
      const expectedCacheKey = 'role-policies-cache-key';

      generateCacheKey.mockReturnValue(expectedCacheKey);
      fetchFromCache.mockResolvedValue(mockRoleWithPolicies);

      await fetchRolePolicies(mockFastify, mockRequest, roleId, userEntityId);

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockFastify.redis,
        expectedCacheKey,
        expect.any(Function),
        86400,
      );
    });

    it('should generate correct cache key using generateRolePoliciesCacheKey', async () => {
      const roleId = 'test-role-789';
      const userEntityId = 'user-entity-789';

      await fetchRolePolicies(mockFastify, mockRequest, roleId, userEntityId);

      expect(generateCacheKey).toHaveBeenCalledWith('role_policies', {
        ...mockRequest,
        raw: {
          ...mockRequest.raw,
          url: `/role-policies/${roleId}`,
        },
      });
    });

    it('should call findByIdWithModulePolicies with correct parameters in fetch function', async () => {
      const roleId = 'test-role-456';
      const userEntityId = 'user-entity-456';

      fetchFromCache.mockImplementation(async (redis, cacheKey, fetchFn) => {
        return await fetchFn();
      });

      await fetchRolePolicies(mockFastify, mockRequest, roleId, userEntityId);

      expect(findByIdWithModulePolicies).toHaveBeenCalledWith(
        mockFastify,
        userEntityId,
        roleId,
        true,
      );
    });

    it('should return the result from fetchFromCache', async () => {
      const expectedResult = { id: 'role-123', name: 'Test Role' };
      fetchFromCache.mockResolvedValue(expectedResult);

      const result = await fetchRolePolicies(mockFastify, mockRequest, 'role-id', 'user-id');

      expect(result).toBe(expectedResult);
    });

    it('should handle errors from fetchFromCache', async () => {
      const error = new Error('Cache fetch failed');
      fetchFromCache.mockRejectedValue(error);

      await expect(
        fetchRolePolicies(mockFastify, mockRequest, 'role-id', 'user-id'),
      ).rejects.toThrow('Cache fetch failed');
    });

    it('should handle errors from findByIdWithModulePolicies', async () => {
      const error = new Error('Repository fetch failed');
      findByIdWithModulePolicies.mockRejectedValue(error);

      fetchFromCache.mockImplementation(async (redis, cacheKey, fetchFn) => {
        return await fetchFn();
      });

      await expect(
        fetchRolePolicies(mockFastify, mockRequest, 'role-id', 'user-id'),
      ).rejects.toThrow('Repository fetch failed');
    });
  });

  describe('checkPolicies', () => {
    it('should return early if no policy is required', async () => {
      await checkPolicies(mockRequest, mockFastify, {});
      expect(findByIdWithModulePolicies).not.toHaveBeenCalled();
    });

    it('should return undefined when no policy is required', async () => {
      const result = await checkPolicies(mockRequest, mockFastify, {});
      expect(result).toBeUndefined();
    });

    it('should throw error if role is not found', async () => {
      findByIdWithModulePolicies.mockResolvedValue(null);

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
        'error.roles.sentence.noRolePolicyAccess',
      );

      expect(roleError.noRolePolicyAccess).toHaveBeenCalled();
    });

    it('should throw error when roleWithPolicies is null', async () => {
      findByIdWithModulePolicies.mockResolvedValue(null);

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow();
      expect(roleError.noRolePolicyAccess).toHaveBeenCalled();
    });

    it('should throw error when roleWithPolicies is undefined', async () => {
      findByIdWithModulePolicies.mockResolvedValue(undefined);

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow();
      expect(roleError.noRolePolicyAccess).toHaveBeenCalled();
    });

    it('should throw error if hierarchy modules are not found', async () => {
      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {
          merchant: [],
        },
      });

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
        'error.roles.sentence.noHierarchyModuleAccess',
      );

      expect(roleError.noHierarchyModuleAccess).toHaveBeenCalled();
    });

    it('should throw error when hierarchyModules is undefined', async () => {
      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {},
      });

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow();
      expect(roleError.noHierarchyModuleAccess).toHaveBeenCalled();
    });

    it('should throw error when hierarchy key does not exist in modules', async () => {
      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {
          merchant: [],
        },
      });

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow();
      expect(roleError.noHierarchyModuleAccess).toHaveBeenCalled();
    });

    it('should throw error if module policy is not found', async () => {
      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {
          root: [
            {
              id: 'module-1',
              name: 'dashboard',
              policies: {
                canView: true,
              },
            },
          ],
        },
      });

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
        'error.roles.sentence.moduleAccessDenied',
      );
    });

    it('should throw error when modulePolicy is not found (undefined)', async () => {
      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {
          organisation: [
            {
              name: 'different-module',
              policies: {
                read: true,
              },
            },
          ],
        },
      });

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow();
      expect(roleError.moduleAccessDenied).toHaveBeenCalled();
    });

    it('should throw error if policy action is not allowed', async () => {
      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {
          root: [
            {
              id: 'module-1',
              name: 'user',
              policies: {
                canEdit: true,
              },
            },
          ],
        },
      });

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
        'error.roles.sentence.actionNotAllowed',
      );
    });

    it('should throw error when policies is null', async () => {
      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {
          organisation: [
            {
              name: 'user',
              policies: null,
            },
          ],
        },
      });

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow();
      expect(roleError.actionNotAllowed).toHaveBeenCalled();
    });

    it('should throw error when policies is undefined', async () => {
      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {
          organisation: [
            {
              name: 'user',
            },
          ],
        },
      });

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow();
      expect(roleError.actionNotAllowed).toHaveBeenCalled();
    });

    it('should throw error when specific action is false', async () => {
      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {
          organisation: [
            {
              name: 'user',
              policies: {
                read: false,
                write: true,
              },
            },
          ],
        },
      });

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow();
      expect(roleError.actionNotAllowed).toHaveBeenCalled();
    });

    it('should throw error when specific action is undefined', async () => {
      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {
          organisation: [
            {
              name: 'user',
              policies: {
                write: true,
              },
            },
          ],
        },
      });

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow();
      expect(roleError.actionNotAllowed).toHaveBeenCalled();
    });

    it('should pass when all conditions are met', async () => {
      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {
          organisation: [
            {
              name: 'user',
              policies: {
                read: true,
                write: true,
              },
            },
          ],
        },
      });

      const result = await checkPolicies(mockRequest, mockFastify, mockRouteConfig);
      expect(result).toBeUndefined();
    });

    it('should allow organisation users to access their own organisation', async () => {
      const orgId = 'org-1';
      mockRequest.userEntity = { id: orgId, hierarchy: 'organisation' };
      mockRequest.entity = { id: orgId, hierarchy: 'organisation' };

      findByIdWithModulePolicies.mockResolvedValue({
        id: 'role-1',
        name: 'Admin Role',
        modules: {
          organisation: [
            {
              name: 'user',
              policies: {
                read: true,
                write: true,
              },
            },
          ],
        },
      });

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).resolves.not.toThrow();
    });
  });

  describe('hasEntityAccess scenarios', () => {
    it('should throw error before policy checks when hasEntityAccess fails', async () => {
      const requestWithNoAccess = {
        ...mockRequest,
        userEntity: {
          id: 'user-123',
          hierarchy: 'merchant',
        },
        entity: {
          id: 'different-entity-456',
          hierarchy: 'merchant',
        },
      };

      await expect(
        checkPolicies(requestWithNoAccess, mockFastify, mockRouteConfig),
      ).rejects.toThrow('error.roles.sentence.noEntityAccess');

      expect(roleError.noEntityAccess).toHaveBeenCalled();
    });

    it('should pass entity access check for root user', async () => {
      mockRequest.userEntity.hierarchy = 'root';

      const result = await checkPolicies(mockRequest, mockFastify, mockRouteConfig);
      expect(result).toBeUndefined();
    });

    it('should pass entity access check for organisation accessing own organisation', async () => {
      mockRequest.userEntity = {
        id: 'entity-123',
        hierarchy: 'organisation',
      };
      mockRequest.entity = {
        id: 'entity-123',
        hierarchy: 'organisation',
      };

      const result = await checkPolicies(mockRequest, mockFastify, mockRouteConfig);
      expect(result).toBeUndefined();
    });

    it('should pass entity access check for organisation accessing child merchant', async () => {
      mockRequest.userEntity = {
        id: 'parent-entity-123',
        hierarchy: 'organisation',
      };
      mockRequest.entity = {
        id: 'merchant-123',
        hierarchy: 'merchant',
      };
      mockRequest.parentEntity = {
        id: 'parent-entity-123',
      };

      const result = await checkPolicies(mockRequest, mockFastify, mockRouteConfig);
      expect(result).toBeUndefined();
    });

    it('should pass entity access check for merchant accessing own merchant', async () => {
      mockRequest.userEntity = {
        id: 'merchant-123',
        hierarchy: 'merchant',
      };
      mockRequest.entity = {
        id: 'merchant-123',
        hierarchy: 'merchant',
      };

      const result = await checkPolicies(mockRequest, mockFastify, mockRouteConfig);
      expect(result).toBeUndefined();
    });

    it('should fail entity access check for unknown hierarchy', async () => {
      mockRequest.userEntity.hierarchy = 'unknown';

      await expect(checkPolicies(mockRequest, mockFastify, mockRouteConfig)).rejects.toThrow(
        'error.roles.sentence.noEntityAccess',
      );
    });
  });
});
