import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { ModuleRepository } from '#src/modules/core/repository/index.js';
import { RoleValidation } from '#src/modules/core/validations/index.js';
import { NAVIGATION_TYPES } from '#src/modules/user/constants/department.constant.js';
import { RoleConstant } from '#src/modules/user/constants/index.js';
import { RoleError } from '#src/modules/user/errors/index.js';
import {
  PolicyRepository,
  RoleModuleRepository,
  RoleRepository,
} from '#src/modules/user/repository/index.js';
import { getModulePolicyOptions } from '#src/modules/user/services/department.service.js';
import { clearCache, clearCacheWithPrefix } from '#src/utils/cache.util.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';
import { generateRolePoliciesCacheKey } from '#src/utils/role-policies.util.js';

const POLICIES = RoleConstant.POLICIES;
const {
  MODULE_NAMES: { ROLE },
  MODULE_METHODS: { INDEX, VIEW, NAVIGATION },
} = CoreConstant;

const MODULE = ROLE;

/**
 * Retrieves all role entries for a given entity.
 * @param {Object} request - The request object.
 * @returns {Promise<Object[]>} List of role entries.
 */
export const index = async (request) => {
  const { entity } = request;
  const query = {
    ...request.query,
    filter_entityId_eq: entity.id,
  };
  return await RoleRepository.findAll(request.server, query);
};

/**
 * Retrieves a specific role by ID.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} The role entry.
 */
export const view = async (request) => {
  const {
    entity,
    params: { id },
    server,
  } = request;

  const role = await RoleRepository.findByIdWithModulePolicies(server, entity.id, id);

  if (!role) {
    throw CoreError.dataNotFound({ data: 'common.label.role', attribute: 'ID', value: id });
  }

  return role;
};

/**
 * Creates a new role.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} The newly created role.
 * @throws {Error} If creation fails.
 */
export const create = async (request) => {
  const {
    body,
    entity,
    server,
    authInfo: { id: authInfoId },
  } = request;

  const {
    parentId = null,
    departmentId,
    name,
    description = null,
    status = CoreConstant.COMMON_STATUSES.ACTIVE,
    modules = [],
  } = body;

  await RoleValidation.validateRoleName(server, entity.id, name);

  let parentPath = null;
  if (parentId) {
    const parentRole = await RoleRepository.findById(server, parentId, entity.id);
    if (!parentRole) {
      throw CoreError.dataNotFound({
        data: 'common.label.parentRole',
        attribute: 'ID',
        value: parentId,
      });
    } else {
      parentPath = parentRole.path;
    }
  }

  const filteredModules = await RoleValidation.validateAndFilterModulePolicies(
    server,
    modules,
    entity.hierarchy,
    parentId,
  );

  return withTransaction(server, {}, async (transaction) => {
    const safeLabel = name
      .trim()
      .toLowerCase()
      .replace(/[^a-z0-9_]/g, '_')
      .replace(/_+/g, '_');
    const finalPath = parentPath ? `${parentPath}.${safeLabel}` : safeLabel;
    const newRoleData = {
      parentId,
      entityId: entity.id,
      departmentId,
      name,
      description,
      status,
      path: finalPath,
    };

    const newRole = await RoleRepository.create(server, newRoleData, {
      transaction,
      authInfoId,
    });

    const auditModelMapping = {
      Role: {
        afterState: newRole,
      },
    };

    const { roleModuleAuditData, policyAuditData } = await createRoleModulePolicies(
      server,
      newRole.id,
      filteredModules,
      authInfoId,
      transaction,
    );

    // Add RoleModule and Policy audit data to the mapping
    if (roleModuleAuditData.length > 0) {
      auditModelMapping.RoleModule = roleModuleAuditData.map((roleModule) => ({
        afterState: roleModule.toJSON(),
      }));
    }

    if (policyAuditData.length > 0) {
      auditModelMapping.Policy = policyAuditData.map((policy) => ({
        afterState: policy.toJSON(),
      }));
    }

    await clearCacheWithPrefix(server.redis, `${MODULE}_${INDEX}`);

    return {
      result: newRole,
      auditModelMapping,
    };
  });
};

/**
 * Updates an existing role.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} The updated role.
 * @throws {Error} If update fails.
 */
export const update = async (request) => {
  const {
    body,
    entity,
    userEntity,
    server,
    params,
    authInfo: { id: authInfoId, roleId: userRoleId },
  } = request;
  const { id } = params;

  const { name, departmentId, description = null, status, modules = [], version } = body;

  const roleToUpdate = await RoleRepository.findById(server, id, entity.id);
  if (!roleToUpdate) {
    throw CoreError.dataNotFound({ data: 'common.label.role', attribute: 'ID', value: id });
  }
  const originalName = roleToUpdate.name;
  const originalPath = roleToUpdate.path;

  const userRole = await RoleRepository.findById(server, userRoleId, entity.id);
  if (!userRole) {
    throw CoreError.dataNotFound({
      data: 'common.label.role',
      attribute: 'ID',
      value: userRoleId,
    });
  }

  if (
    !RoleValidation.validateHierarchy(
      userRole.path,
      roleToUpdate.path,
      userEntity.hierarchy,
      entity.hierarchy,
    )
  ) {
    throw RoleError.updateNotAllowedForHierarchy();
  }

  await RoleValidation.validateRoleName(server, entity.id, name, id);

  const filteredModules = await RoleValidation.validateAndFilterModulePolicies(
    server,
    modules,
    entity.hierarchy,
    roleToUpdate.parentId,
  );

  const descendants = await RoleRepository.findDescendantsByParentPath(
    server,
    entity.id,
    roleToUpdate.path,
  );

  return withTransaction(server, {}, async (transaction) => {
    let updatedPath = roleToUpdate.path;
    if (name && name !== originalName) {
      const formattedName = name.replace(/\s+/g, '_');
      updatedPath = updateRolePath(roleToUpdate.path, formattedName);
    }

    const updatedRoleData = {
      name: name || originalName,
      departmentId: departmentId || roleToUpdate.departmentId,
      description,
      status,
      path: updatedPath,
      version,
    };

    const updatedRole = await RoleRepository.update(roleToUpdate, updatedRoleData, {
      transaction,
      authInfoId,
    });

    const auditModelMapping = {
      Role: {
        afterState: updatedRole.toJSON(),
      },
    };

    if (name && name !== originalName) {
      await updateDescendantRolesPaths(
        originalPath,
        updatedPath,
        authInfoId,
        descendants,
        transaction,
      );
    }

    const { roleModuleAuditData, policyAuditData } = await updateRoleModulePolicies(
      server,
      updatedRole,
      filteredModules,
      authInfoId,
      transaction,
      descendants,
    );

    // Add RoleModule audit data
    if (roleModuleAuditData.length > 0) {
      auditModelMapping.RoleModule = roleModuleAuditData;
    }

    // Add Policy audit data
    if (policyAuditData.length > 0) {
      auditModelMapping.Policy = policyAuditData;
    }

    await clearRoleCache(server, request, updatedRole.id);

    return {
      result: updatedRole,
      auditModelMapping,
    };
  });
};

/**
 * Updates the status of a role.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} The updated role.
 * @throws {Error} If role is not found or update fails.
 */
export const updateStatus = async (request) => {
  const { body, server, params, entity } = request;
  const { id } = params;
  const { status } = body;

  const roleRow = await RoleRepository.findById(server, id, entity.id);
  if (!roleRow) {
    throw CoreError.dataNotFound({ data: 'common.label.role', attribute: 'ID', value: id });
  }

  const updatedRole = await RoleRepository.update(roleRow, { status }, request.user);

  await clearRoleCache(server, request, id);

  return {
    result: updatedRole,
    auditModelMapping: {
      Role: {
        afterState: updatedRole.toJSON(),
      },
    },
  };
};

/**
 * Retrieves module policy options.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} Module policy options.
 */
export const options = async (request) => await getModulePolicyOptions(request);

/**
 * Retrieves user menus based on role policies.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} The user menu structure.
 * @throws {Error} If user role is not found.
 */
export const navigations = async (request) => {
  const { server, authInfo, entity } = request;

  const roleId = authInfo.roleId;
  const userRole = await RoleRepository.findById(server, roleId, entity.id, true);
  if (!userRole) {
    throw CoreError.dataNotFound({ data: 'common.label.role', attribute: 'ID', value: roleId });
  }

  const allModules = await ModuleRepository.findAll(server);
  const filteredModules = allModules.filter((module) => {
    return !module.navigationType.includes(NAVIGATION_TYPES.NONE);
  });

  const roleModulePolicies = await RoleModuleRepository.findAllByRoleIdWithPolicyAndModules(
    server,
    userRole.id,
  );

  const modulePoliciesMap = roleModulePolicies.reduce((acc, rm) => {
    acc[rm.moduleId] = rm.policy;
    return acc;
  }, {});

  const byParent = new Map();
  const hierarchyLevels = new Set();
  const moduleInfo = new Map();

  // Build all lookup tables in one pass through the modules
  filteredModules.forEach((module) => {
    // Create parent-to-children mapping
    if (!byParent.has(module.parentId)) {
      byParent.set(module.parentId, []);
    }
    byParent.get(module.parentId).push(module);

    hierarchyLevels.add(module.hierarchy);

    // Store basic module information and permissions
    const policy = modulePoliciesMap[module.id];
    const hasViewPolicy = policy?.canView || false;

    moduleInfo.set(module.id, {
      module,
      hasViewPolicy,
      hasViewInSubtree: null, // Will be calculated
    });
  });

  // Recursive function to calculate if user can view anything in subtree
  const computeSubtreePermissions = (moduleId) => {
    const info = moduleInfo.get(moduleId);
    if (info.hasViewInSubtree !== null) {
      return info.hasViewInSubtree; // Already calculated
    }

    // For leaf nodes (modules with navigationUrl), use their own permission
    if (info.module.navigationUrl) {
      info.hasViewInSubtree = info.hasViewPolicy;
      return info.hasViewInSubtree;
    }

    // For parent nodes, check if any children are viewable
    let canViewSubtree = false;
    const children = byParent.get(moduleId) || [];
    for (const child of children) {
      if (computeSubtreePermissions(child.id)) {
        canViewSubtree = true;
        break; // Found at least one viewable child
      }
    }

    info.hasViewInSubtree = canViewSubtree;
    return canViewSubtree;
  };

  // Calculate permissions for all modules
  filteredModules.forEach((module) => {
    computeSubtreePermissions(module.id);
  });

  // Build menu structure
  const buildMenuStructure = (parentId, hierarchy, navigationType) => {
    const children = byParent.get(parentId) || [];
    const relevantModules = children.filter(
      (m) =>
        m.hierarchy === hierarchy &&
        m.navigationType.includes(navigationType) &&
        moduleInfo.get(m.id)?.hasViewInSubtree,
    );

    return relevantModules.reduce((result, module) => {
      const hasVisibleChildren = (byParent.get(module.id) || []).some(
        (child) => moduleInfo.get(child.id)?.hasViewInSubtree,
      );

      if (hasVisibleChildren) {
        // Module has children - create menu group
        const childMenu = buildMenuStructure(module.id, hierarchy, navigationType);
        if (childMenu.length > 0) {
          result.push({ [module.translationKey]: childMenu });
        }
      } else if (module.navigationUrl) {
        // Leaf module - create direct menu item
        result.push({
          name: module.translationKey,
          url: module.navigationUrl,
        });
      }

      return result;
    }, []);
  };

  // Build the complete menu structure for all hierarchies and navigation types
  const menuStructure = {};
  const navigationTypes = [NAVIGATION_TYPES.SIDE, NAVIGATION_TYPES.TOP, NAVIGATION_TYPES.PERSONAL];

  // Process each hierarchy level separately
  for (const hierarchy of hierarchyLevels) {
    menuStructure[hierarchy] = {};

    // Create navigation menus for each type at this hierarchy
    for (const navigationType of navigationTypes) {
      menuStructure[hierarchy][navigationType.toLowerCase()] = buildMenuStructure(
        null, // Start from root modules (parentId = null)
        hierarchy,
        navigationType,
      );
    }
  }

  return menuStructure;
};

/**
 * Creates role module policies and their associated policy settings.
 * @param {Object} server - The server object.
 * @param {string} roleId - The ID of the role to create policies for.
 * @param {Array} filteredModules - Array of module objects with moduleId and policies.
 * @param {string} authInfoId - The ID of the user creating the policies.
 * @param {Object} transaction - The database transaction.
 * @returns {Promise<void>} - Returns nothing if successful.
 */
const createRoleModulePolicies = async (
  server,
  roleId,
  filteredModules,
  authInfoId,
  transaction,
) => {
  if (filteredModules.length === 0) {
    return {
      roleModuleAuditData: [],
      policyAuditData: [],
    };
  }

  const moduleMap = new Map();
  const roleModuleData = filteredModules.map((module) => {
    moduleMap.set(module.moduleId, module);
    return { roleId, moduleId: module.moduleId };
  });

  const createdRoleModules = await RoleModuleRepository.bulkCreate(server, roleModuleData, {
    transaction,
    authInfoId,
  });

  const policyData = createdRoleModules.map((roleModule) => {
    const module = moduleMap.get(roleModule.moduleId);
    return {
      parentId: roleModule.id,
      ...Object.fromEntries(module.policies.map((policy) => [policy, true])),
    };
  });

  const createdPolicies = await PolicyRepository.bulkCreate(server, policyData, {
    transaction,
    authInfoId,
  });

  return {
    roleModuleAuditData: createdRoleModules,
    policyAuditData: createdPolicies,
  };
};

/**
 * Updates role module policies and their associated policy settings.
 * @param {Object} server - The server object.
 * @param {Object} role - The role object to update policies for.
 * @param {Array} filteredModules - Array of module objects with moduleId and policies.
 * @param {string} authInfoId - The ID of the user updating the policies.
 * @param {Object} transaction - The database transaction.
 * @param {Array} descendants - Array of descendant roles that inherit permissions.
 * @returns {Promise<void>} - Returns nothing if successful.
 */
const updateRoleModulePolicies = async (
  server,
  role,
  filteredModules,
  authInfoId,
  transaction,
  descendants,
) => {
  // Pre-compute reusable values
  const requestedModuleIds = new Set(filteredModules.map(({ moduleId }) => moduleId));
  const allPoliciesFalse = Object.fromEntries(POLICIES.map((p) => [p, false]));

  const existingPolicies = await RoleModuleRepository.findAllByRoleIdWithPolicy(server, role.id);

  const policiesToRemove = [];
  const existingRoleModulesMap = new Map();
  const roleModuleAuditData = [];
  const policyAuditData = [];

  existingPolicies.forEach((policy) => {
    if (requestedModuleIds.has(policy.moduleId)) {
      existingRoleModulesMap.set(policy.moduleId, {
        id: policy.id,
        moduleId: policy.moduleId,
        roleId: policy.roleId,
      });
    } else {
      policiesToRemove.push(policy);
    }
  });

  if (policiesToRemove.length > 0) {
    await Promise.all(
      policiesToRemove.map((policy) =>
        Promise.all([
          updatePolicies(server, policy.id, allPoliciesFalse, authInfoId, transaction),
          updateDescendantPolicies(
            server,
            descendants,
            policy.moduleId,
            allPoliciesFalse,
            authInfoId,
            transaction,
          ),
        ]),
      ),
    );
  }

  // Prepare upsert data
  const roleModuleUpsertData = filteredModules.map((module) => {
    const existingRoleModule = existingRoleModulesMap.get(module.moduleId);
    return {
      ...(existingRoleModule && { id: existingRoleModule.id }),
      roleId: role.id,
      moduleId: module.moduleId,
    };
  });

  const roleModuleConflictFields = ['roleId', 'moduleId'];
  const upsertedRoleModules = await RoleModuleRepository.bulkUpsert(
    server,
    roleModuleUpsertData,
    roleModuleConflictFields,
    {
      transaction,
      authInfoId,
    },
  );

  // Update map with upserted modules
  upsertedRoleModules.forEach((rm) => {
    existingRoleModulesMap.set(rm.moduleId, rm);

    roleModuleAuditData.push({
      afterState: rm.toJSON(),
    });
  });

  // Helper function to create policy object
  const createPolicyObject = (requestedPolicies) => {
    const policies = { ...allPoliciesFalse };
    requestedPolicies.forEach((policy) => {
      policies[policy] = true;
    });
    return policies;
  };

  // Prepare policy upsert data
  const policiesUpsertData = filteredModules.map((module) => {
    const roleModule = existingRoleModulesMap.get(module.moduleId);
    const existingPolicy = existingPolicies.find((policy) => policy.id === roleModule.id)?.policy;

    return {
      parentId: roleModule.id,
      ...createPolicyObject(module.policies),
      // Only include createdBy for new records
      ...(existingPolicy ? {} : { createdBy: authInfoId }),
      updatedBy: authInfoId,
    };
  });

  // Execute both operations concurrently
  const policyConflictFields = ['parentId'];
  const [upsertedPolicies] = await Promise.all([
    PolicyRepository.bulkUpsert(server, policiesUpsertData, policyConflictFields, {
      transaction,
    }),
    Promise.all(
      filteredModules.map((module) =>
        updateDescendantPolicies(
          server,
          descendants,
          module.moduleId,
          createPolicyObject(module.policies),
          authInfoId,
          transaction,
        ),
      ),
    ),
  ]);

  // Track Policy changes for audit
  upsertedPolicies.forEach((policy) => {
    policyAuditData.push({
      afterState: policy.toJSON(),
    });
  });

  return {
    roleModuleAuditData,
    policyAuditData,
  };
};

/**
 * Updates a role's path.
 * @param {string} oldPath - The original path.
 * @param {string} newName - The new role name.
 * @returns {string} The updated path.
 */
const updateRolePath = (oldPath, newName) => {
  const pathParts = oldPath.split('.');
  pathParts[pathParts.length - 1] = newName;
  return pathParts.join('.');
};

/**
 * Updates paths of descendant roles.
 * @param {string} oldPath - The old path.
 * @param {string} newPath - The new path.
 * @param {string} authInfoId - The user ID making the update.
 * @param {Object} descendants - The descendants object.
 * @param {Object} transaction - The database transaction.
 */
const updateDescendantRolesPaths = async (
  oldPath,
  newPath,
  authInfoId,
  descendants,
  transaction,
) => {
  const auditData = [];

  for (const descendant of descendants) {
    const newDescendantPath = descendant.path.replace(oldPath, newPath);
    const updatedDescendant = await RoleRepository.update(
      descendant,
      { path: newDescendantPath },
      { transaction, authInfoId },
    );

    auditData.push({
      afterState: updatedDescendant.toJSON(),
    });
  }

  return auditData;
};

/**
 * Updates policies for descendant roles.
 * @param {Object} server - The server object.
 * @param {string} descendants - The descendants object.
 * @param {string} moduleId - The module ID.
 * @param {Object} parentPolicy - The parent policy.
 * @param {string} authInfoId - The user ID making the update.
 * @param {Object} transaction - The database transaction.
 */
const updateDescendantPolicies = async (
  server,
  descendants,
  moduleId,
  parentPolicies,
  authInfoId,
  transaction,
) => {
  const descendantRoleIds = descendants.map((role) => role.id);

  // 1) Preload all RoleModules for descendants
  const roleModules = await RoleModuleRepository.findAllByRoleIdsAndModuleId(
    server,
    descendantRoleIds,
    moduleId,
    { transaction, authInfoId },
  );

  // Map for quick lookup
  const roleModuleMap = new Map(roleModules.map((rm) => [rm.roleId, rm]));

  // 2) Preload all Policy in one go
  const roleModuleIds = Array.from(roleModuleMap.values()).map((rm) => rm.id);
  const allPolicies = await PolicyRepository.findByParentIds(server, roleModuleIds, {
    transaction,
    authInfoId,
  });

  // Map by roleModuleId
  const policyMap = new Map(allPolicies.map((p) => [p.parentId, p]));

  // 3) Update policy for descendants
  for (const roleId of descendantRoleIds) {
    const roleModule = roleModuleMap.get(roleId);
    const policies = policyMap.get(roleModule?.id);

    if (policies) {
      const current = policies.toJSON();
      const updated = {};

      for (const policy of POLICIES) {
        const hasNow = Boolean(current[policy]);
        const parentAllows = Boolean(parentPolicies[policy]);
        updated[policy] = hasNow && parentAllows;
      }

      await updatePolicies(server, roleModule.id, updated, authInfoId, transaction);
    }
  }
};

/**
 * Updates policy settings.
 * @param {Object} server - The server object.
 * @param {string} parentId - The parent ID.
 * @param {Object} policies - The policy settings to update.
 * @param {string} authInfoId - The user ID making the update.
 * @param {Object} transaction - The database transaction.
 */
const updatePolicies = async (server, parentId, policies, authInfoId, transaction) => {
  const allPolicies = POLICIES.reduce((acc, policy) => {
    acc[policy] = false;
    return acc;
  }, {});

  const updatedPolicies = {
    ...allPolicies,
    ...policies,
  };

  await PolicyRepository.upsert(
    server,
    { parentId, ...updatedPolicies },
    {
      transaction,
      authInfoId,
    },
  );
};

/**
 * Clears cache entries related to roles.
 * @param {Object} server - The server object.
 * @param {Object} request - The request object.
 * @param {string|number} roleId - The role ID.
 * @returns {Promise<void>}
 */
const clearRoleCache = async (server, request, roleId) => {
  const policiesCacheKey = generateRolePoliciesCacheKey(request, roleId);
  await clearCache(server.redis, policiesCacheKey);
  await clearCacheWithPrefix(server.redis, `${MODULE}_${INDEX}`);
  await clearCacheWithPrefix(server.redis, `${MODULE}_${VIEW}_${roleId}`);
  await clearCacheWithPrefix(server.redis, `${MODULE}_${NAVIGATION}_${roleId}`);
};
