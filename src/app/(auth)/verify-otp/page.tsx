'use client';

import { AuthLayout } from '@/components/application-ui/layouts/auth-layout';
import { VerifyOTPForm } from '@/components/features/auth/forms';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { type JSX } from 'react';

const Page = (): JSX.Element => {
  usePageTitle('common.action.verifyOtp');
  return (
    <AuthLayout maxWidth="sm">
      <VerifyOTPForm />
    </AuthLayout>
  );
};

export default Page;
