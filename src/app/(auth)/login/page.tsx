'use client';

import { AuthLayout } from '@/components/application-ui/layouts/auth-layout';
import { LoginForm } from '@/components/features/auth/forms';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { type JSX } from 'react';

const Page = (): JSX.Element => {
  usePageTitle('common.label.signIn');
  return (
    <AuthLayout maxWidth="lg">
      <LoginForm />
    </AuthLayout>
  );
};

export default Page;
