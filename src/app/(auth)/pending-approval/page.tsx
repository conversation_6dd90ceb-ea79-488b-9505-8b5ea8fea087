'use client';

import { AuthLayout } from '@/components/application-ui/layouts/auth-layout';
import { PendingApprovalForm } from '@/components/features/auth/forms';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { type JSX } from 'react';

const Page = (): JSX.Element => {
  usePageTitle('common.label.accountPendingApproval');
  return (
    <AuthLayout>
      <PendingApprovalForm />
    </AuthLayout>
  );
};

export default Page;
