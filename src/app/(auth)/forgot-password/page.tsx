'use client';

import { AuthLayout } from '@/components/application-ui/layouts/auth-layout';
import { ForgotPasswordForm } from '@/components/features/auth/forms';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { type JSX } from 'react';

const Page = (): JSX.Element => {
  usePageTitle('common.action.recoverPassword');
  return (
    <AuthLayout maxWidth="sm">
      <ForgotPasswordForm />
    </AuthLayout>
  );
};

export default Page;
