'use client';

import { AvatarIcon } from '@/components/base/data-display/avatar';
import { PageHeading } from '@/components/base/headings/page-heading';
import { useCustomization } from '@/hooks/ui/use-customization.hook';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import DashboardTwoToneIcon from '@mui/icons-material/DashboardTwoTone';
import { Box, Container } from '@mui/material';
import { type JSX } from 'react';
import { useTranslation } from 'react-i18next';

const pageMeta = {
  title: 'common.label.dashboard',
};

const DashboardPage = (): JSX.Element => {
  usePageTitle(pageMeta.title);
  const { stretch } = useCustomization();
  const { t } = useTranslation();
  const translatedTitle = t(pageMeta.title);

  return (
    <>
      {pageMeta.title && (
        <PageHeading
          iconBox={
            <AvatarIcon
              icon={<DashboardTwoToneIcon />}
              state="honeyGold"
              size={44}
              variant="rounded"
            />
          }
          title={translatedTitle}
        />
      )}
      <Box pb={{ xs: 2, sm: 3 }}>
        <Container maxWidth={stretch ? false : 'xl'}></Container>
      </Box>
    </>
  );
};

export default DashboardPage;
