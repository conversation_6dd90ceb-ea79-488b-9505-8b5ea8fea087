'use client';

import { FormLayout } from '@/components/application-ui/layouts/form-layout';
import { SafetySettingForm } from '@/components/features/settings/safety-setting';
import { type JSX } from 'react';

/**
 * Page metadata configuration for the safety settings page
 */
const pageMeta = {
  title: 'common.label.safetySetting',
  description: 'settings.sentence.safetyPageDesc',
};

/**
 * A page component that allows users to manage their safety settings including
 * including session lifetime, password policies, two-factor authentication settings,
 * and other security-related configurations with confirmation dialogs for changes.
 */
const SafetySettingPage = (): JSX.Element => {
  return (
    <FormLayout
      title={pageMeta.title}
      description={pageMeta.description}
      showSubmit={true}
      showReset={true}
      unsaved={true}
      FormComponent={SafetySettingForm}
    />
  );
};

export default SafetySettingPage;
