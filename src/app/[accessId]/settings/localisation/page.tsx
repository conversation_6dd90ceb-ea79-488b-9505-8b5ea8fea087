'use client';

import { FilterableSidebarLayout } from '@/components/application-ui/layouts/filterable-sidebar-layout';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { transformText } from '@/utils/text-transform.util';
import { type JSX } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Page metadata configuration for the localisation settings page
 */
const pageMeta = {
  title: 'common.label.localisation',
  description: 'settings.sentence.localisationPageDesc',
};

/**
 * A page component that allows users to manage localisation settings
 */
const LocalisationPage = (): JSX.Element => {
  usePageTitle(pageMeta.title);

  const { t } = useTranslation();
  const translatedTitle = t(pageMeta.title);
  const transformedTitle = transformText(translatedTitle, 'sentenceCase');
  const translatedDescription = t(pageMeta.description);
  const transformedDescription = transformText(translatedDescription, 'sentenceCase');

  return (
    <FilterableSidebarLayout
      pageHeading={{
        title: transformedTitle,
        description: transformedDescription,
      }}
      sidebarContent={<>mock sidebar</>}
    >
      mock listing
    </FilterableSidebarLayout>
  );
};

export default LocalisationPage;
