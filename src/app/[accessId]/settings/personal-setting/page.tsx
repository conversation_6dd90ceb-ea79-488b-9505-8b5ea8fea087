'use client';

import { FormLayout } from '@/components/application-ui/layouts/form-layout';
import { PersonalSettingForm } from '@/components/features/settings/personal-setting';
import { type JSX } from 'react';

/**
 * Page metadata configuration for the personal settings page
 */
const pageMeta = {
  title: 'common.label.personalSetting',
};

/**
 * A page component that allows users to manage their personal settings including
 * appearance preferences, pagination settings, date/time formats, language, and timezone.
 */
const PersonalSettingPage = (): JSX.Element => {
  return (
    <FormLayout
      title={pageMeta.title}
      showSubmit={true}
      showReset={true}
      unsaved={true}
      FormComponent={PersonalSettingForm}
    />
  );
};

export default PersonalSettingPage;
