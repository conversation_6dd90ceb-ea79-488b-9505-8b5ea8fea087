'use client';

import { FormLayout } from '@/components/application-ui/layouts/form-layout';
import { ThemesSettingForm } from '@/components/features/settings/themes-setting';
import { type JSX } from 'react';

/**
 * Page metadata configuration for the themes settings page
 */
const pageMeta = {
  title: 'common.label.themesSetting',
  description: 'settings.sentence.themesPageDesc',
};

/**
 * A page component that allows users to manage their themes settings including
 * alert bar position and toast message position.
 */
const ThemesSettingPage = (): JSX.Element => {
  return (
    <FormLayout
      title={pageMeta.title}
      description={pageMeta.description}
      showSubmit={true}
      showReset={true}
      unsaved={true}
      FormComponent={ThemesSettingForm}
    />
  );
};

export default ThemesSettingPage;
