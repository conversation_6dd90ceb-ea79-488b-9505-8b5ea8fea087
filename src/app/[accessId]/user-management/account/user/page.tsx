'use client';

import { FilterableSidebarLayout } from '@/components/application-ui/layouts/filterable-sidebar-layout';
import { Button } from '@/components/base/inputs/button';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { type JSX } from 'react';
import { useTranslation } from 'react-i18next';

const pageMeta = {
  title: 'common.label.user',
  description: 'user.sentence.pageDesc',
};

/**
 * UserPage - View and manage user accounts
 */
const UserPage = (): JSX.Element => {
  usePageTitle(pageMeta.title);
  const { t } = useTranslation();
  const translatedTitle = t(pageMeta.title);
  const translatedDescription = t(pageMeta.description);

  const pageHeadingConfig = {
    title: translatedTitle,
    description: translatedDescription,
    actions: (
      <Button
        sx={{ mt: { xs: 2, md: 0 } }}
        variant="contained"
        fullWidth
      >
        {t('common.label.newUser')}
      </Button>
    ),
  };

  return (
    <FilterableSidebarLayout
      pageHeading={pageHeadingConfig}
      sidebarContent={<></>}
    >
      <></>
    </FilterableSidebarLayout>
  );
};

export default UserPage;
