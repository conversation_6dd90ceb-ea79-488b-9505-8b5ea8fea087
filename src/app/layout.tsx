import type { Metadata, Viewport } from 'next';
import type { ReactNode } from 'react';
import '@/global.css';
import { NProgress } from '@/components/base/feedback/nprogress';
import { AppStoreProvider } from '@/contexts/app';
import { DocumentLayout } from '@/layouts/document';
import { Provider as QueryProvider } from '@/providers/tanstack/query/Provider';
import { restoreCustomization } from '@/utils/server-side-customization.util';
import { NavigationGuardProvider } from 'next-navigation-guard';

export const dynamic = 'force-dynamic'; // ensure per-request rendering

export const metadata: Metadata = {
  title: {
    default: 'Quantum Play',
    template: `%s | Quantum Play`,
  },
  description: 'A customizable white-label back office',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  colorScheme: 'dark light',
};

interface LayoutProps {
  children: ReactNode;
}

const Layout = async ({ children }: LayoutProps) => {
  const customization = await restoreCustomization();

  return (
    <html lang="en">
      <body>
        <NavigationGuardProvider>
          <AppStoreProvider>
            <QueryProvider>
              <DocumentLayout customization={customization}>
                {/* Keep NProgress high in the tree so it overlays consistently */}
                <NProgress />
                {children}
              </DocumentLayout>
            </QueryProvider>
          </AppStoreProvider>
        </NavigationGuardProvider>
      </body>
    </html>
  );
};

export default Layout;
