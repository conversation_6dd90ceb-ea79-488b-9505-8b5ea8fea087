import type { GridInitialState } from '@mui/x-data-grid-premium';
import { getDefaultToolbarProps } from './DataGrid.util';

// Pagination constants
export const PAGINATION_PAGE_SIZE = 10;
export const PAGINATION_PAGE_SIZE_OPTIONS = [10, 20, 50, 100];

export const DEFAULT_TOOLBAR_PROPS = getDefaultToolbarProps('untitled');

/**
 * Default initial state for DataGrid pagination.
 */
export const DEFAULT_GRID_INITIAL_STATE: GridInitialState = {
  pagination: {
    paginationModel: {
      page: 0,
      pageSize: PAGINATION_PAGE_SIZE,
    },
  },
};
