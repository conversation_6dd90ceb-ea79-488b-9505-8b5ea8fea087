import transformText from '@/utils/text-transform.util';
import { Box } from '@mui/material';
import type { GridActionsColDef, GridColDef, ValueOptions } from '@mui/x-data-grid';
import { enUS, zhCN } from '@mui/x-data-grid/locales';
import { format } from 'date-fns';
import type { TFunction } from 'i18next';
import type { ChangeCaseTransform } from '../../typography/Typography.type';
import { Label, type LabelMap } from '../label';
import type { CustomGridToolbarProps } from './DataGrid.type';

/**
 * Formats current datetime for file naming
 */
export const formatDateTime = () =>
  // Handle user timezone and locale in the future here
  new Date().toISOString().replace(/[:.]/g, '-').replace('T', '_').slice(0, -5);

/**
 * Formats a filename with the current datetime and optional suffix.
 */
export const getFormattedFileName = (baseFileName: string, suffix?: string) => {
  const datetime = formatDateTime();
  if (!suffix) return `${baseFileName}_${datetime}`;

  return `${baseFileName}_${suffix}_${datetime}`;
};

/**
 * Helper function to create a header name from a field name with translation and text transformation
 * @param fieldName - The field name to transform
 * @param t - Translation function
 * @returns Transformed header name
 */
export const createHeaderName = (
  fieldName: string,
  t: (key: string) => string,
  transform?: ChangeCaseTransform
): string => {
  return transformText(t(fieldName), transform ?? 'sentenceCase');
};

/**
 * Function to translate string for DataGrid
 *
 * (Ctrl / Command + P)
 * [EN]: node_modules/@mui/x-data-grid/esm/constants/localeTextConstants.js
 * [ZH]: node_modules/@mui/x-data-grid/esm/locales/zhCN.js
 */
export const useLocaleText = (language: Language, _t: TFunction) => {
  if (language === 'zh') {
    const base = zhCN.components.MuiDataGrid.defaultProps.localeText;
    return {
      ...base,
      // if a string is not translated by MUI yet, we need to replace it manually
      // columnMenuManagePivot: t('common.action.managePivot'),
    };
  }

  // Default to 'en'
  return enUS.components.MuiDataGrid.defaultProps.localeText;
};

/**
 * Returns the column definition for the "name" field.
 *
 * @param {TFunction} t - Translation function used to internationalize the header name.
 * @returns {GridColDef} Column configuration for the "name" field.
 */
export const getNameColumn = (t: TFunction): GridColDef => {
  return {
    field: 'name',
    flex: 1,
    groupable: false,
    headerName: createHeaderName('common.label.name', t),
  };
};

/**
 * Returns the column definition for the "description" field.
 *
 * @param {TFunction} t - Translation function used to internationalize the header name.
 * @returns {GridColDef} Column configuration for the "description" field.
 */
export const getDescriptionColumn = (t: TFunction): GridColDef => {
  return {
    field: 'description',
    flex: 2,
    groupable: false,
    minWidth: 140,
    headerName: createHeaderName('common.label.description', t),
  };
};

/**
 * Returns the column definition for a "status" field in a data grid.
 *
 * @param {Object} params - The parameters for configuring the column.
 * @param {TFunction} params.t - Translation function for internationalization.
 * @param {LabelMap} params.labelMap - Mapping of status values to label components.
 * @param {ValueOptions[]} params.valueOptions - List of selectable status options with labels and values.
 *
 * @returns {GridColDef} The column definition object for the 'status' column.
 */
export const getStatusColumn = ({
  t,
  labelMap,
  valueOptions,
}: {
  t: TFunction;
  labelMap: LabelMap;
  valueOptions: ValueOptions[];
}): GridColDef => {
  return {
    field: 'status',
    flex: 1,
    type: 'singleSelect',
    valueOptions: valueOptions,
    groupable: false,
    headerName: createHeaderName('common.label.status', t),
    renderCell: (params) =>
      params.row.status ? (
        <Label
          value={params.row.status}
          map={labelMap}
          data-testId="status-label"
        />
      ) : null,
  };
};

/**
 * Returns the column definition for an audit field that displays a user (or entity) and a timestamp.
 *
 * This column combines two fields: one representing a user or action performer (`byField`) and another
 * representing the timestamp (`dateField`). It formats the cell to show both pieces of information
 * in a stacked layout and generates a composite header using translations.
 *
 * @param {Object} params - Configuration object.
 * @param {string} params.dateField - The field name for the date/time value in the row data.
 * @param {string} params.byField - The field name for the user or entity responsible for the action.
 * @param {TFunction} params.t - Translation function (usually from i18next).
 *
 * @returns {GridColDef | GridActionsColDef} A column definition suitable for use in a data grid.
 */
export const getAuditColumn = ({
  dateField: field,
  byField,
  t,
}: {
  dateField: string;
  byField: string;
  t: TFunction;
}): GridColDef | GridActionsColDef => {
  const headerByName = createHeaderName(`common.label.${byField}`, t);
  const headerDateName = createHeaderName('common.label.date', t, 'lowercase');
  const headerName = `${headerByName} / ${headerDateName}`;
  return {
    field,
    type: 'dateTime',
    flex: 1,
    groupable: false,
    minWidth: 160,
    headerName: headerName,
    cellClassName: 'no-line-height',
    valueGetter: (v) => new Date(v),
    renderHeader: () => <span data-testid={`${field}-header-cell`}>{headerName}</span>,
    renderCell: (params) => (
      <Box>
        <div>{params.row[byField]}</div>
        <div>{formatDateTimeColumn(params.row[field])}</div>
      </Box>
    ),
  };
};

export const getActionColumn = ({
  t,
  getActions,
}: {
  t: TFunction;
  getActions: GridActionsColDef['getActions'];
}) => {
  return {
    field: 'actions',
    type: 'actions' as const,
    headerName: createHeaderName('common.label.actions', t),
    flex: 0.1,
    hideable: false,
    width: 40,
    getActions,
  };
};

/**
 * Creates default toolbar props for DataGrid.
 * @param fileName - CSV export file name (without extension)
 */
export const getDefaultToolbarProps = (fileName: string): CustomGridToolbarProps => ({
  showQuickFilter: true,
  showDensityButton: true,
  showColumnsButton: true,
  csvOptions: { fileName },
  quickFilterProps: {
    debounceMs: 500,
  },
});

/**
 * Formats a date into the 'yyyy MM dd HH:mm:ss' format.
 *
 * @param {string | number | Date} date - The date to format. Can be a string, number, or Date object.
 * @returns {string} The formatted date string.
 */
export const formatDateTimeColumn = (date: string | number | Date) => {
  return format(date, 'yyyy MM dd HH:mm:ss');
};
