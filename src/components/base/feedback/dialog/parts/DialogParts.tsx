import { Typography } from '@/components/base/typography';
import { useIsMobile } from '@/hooks/ui/use-is-mobile.hook';
import { Box, DialogActions, DialogContent, DialogContentText, DialogTitle } from '@mui/material';
import type { DialogActionButtonsProps, DialogBodyProps } from '../Dialog.type';

/**
 * The body content of a dialog, including title, message, note, and children.
 */
export const DialogBody = ({ title, message, note, children }: DialogBodyProps) => {
  const isMobile = useIsMobile();
  return (
    <Box
      pt={isMobile ? 0 : 0.5}
      textAlign={isMobile ? 'center' : 'left'}
    >
      {title && (
        <DialogTitle
          id="alert-dialog-title"
          variant="h4"
          gutterBottom
        >
          {title}
        </DialogTitle>
      )}
      <DialogContent id="alert-dialog-description">
        {message && (
          <Typography
            variant="h5"
            fontWeight={400}
            sx={{ whiteSpace: 'pre-line' }}
          >
            {message}
          </Typography>
        )}
        {note && (
          <DialogContentText
            variant="subtitle1"
            sx={{ pt: 2 }}
          >
            {note}
          </DialogContentText>
        )}
        {children}
      </DialogContent>
    </Box>
  );
};

/**
 * The action buttons container for a dialog.
 */
export const DialogActionButtons = ({ children }: DialogActionButtonsProps) => {
  const isMobile = useIsMobile();
  return (
    <DialogActions
      sx={(theme) => ({
        backgroundColor:
          theme.palette.mode === 'dark' ? theme.palette.action.hover : theme.palette.grey[50],
        flexDirection: isMobile ? 'column-reverse' : 'row',
        '& > :not(:first-of-type)': {
          marginLeft: isMobile ? 0 : 1,
          marginBottom: isMobile ? 1 : 0,
        },
      })}
    >
      {children}
    </DialogActions>
  );
};
