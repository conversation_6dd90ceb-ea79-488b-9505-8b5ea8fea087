import { Typography } from '@/components/base/typography';
import { Alert } from '@mui/material';
import { useTranslation } from 'react-i18next';
import type { FormAlertProps } from './FormAlert.type';

/**
 * Form alert component
 *
 * @remarks
 * Displays an alert with optional title and message.
 *
 * @example
 * ```tsx
 * <FormAlert severity="error" title="Error" message="Something went wrong." />
 * ```
 */
export const FormAlert = ({
  title,
  message,
  severity = 'info',
  onClose = undefined,
}: FormAlertProps) => {
  const { t } = useTranslation();
  if (!message) return null;

  return (
    <Alert
      severity={severity}
      onClose={onClose}
    >
      {title && (
        <Typography
          variant="h5"
          caseTransform="sentenceCase"
        >
          {title}
        </Typography>
      )}
      <Typography caseTransform="sentenceCase">{t(message)}</Typography>
    </Alert>
  );
};

export default FormAlert;
