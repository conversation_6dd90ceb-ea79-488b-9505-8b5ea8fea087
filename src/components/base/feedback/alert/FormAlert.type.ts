import type { ChangeCaseTransform } from '@/components/base/typography';
import type { AlertColor } from '@mui/material/Alert';

/**
 * Props for the FormAlert component.
 * Used to display contextual messages within forms.
 */
export interface FormAlertProps {
  /**
   * Short heading text for the alert.
   * Optional. If omitted, only the message will be shown.
   */
  title?: string;

  /**
   * Main content of the alert.
   * Optional. If omitted, only the title will be shown.
   */
  message?: string;

  /**
   * The severity level of the alert.
   * Maps directly to MUI's `AlertColor` ('error' | 'warning' | 'info' | 'success').
   * @default 'info'
   */
  severity?: AlertColor;
  onClose?: () => void;

  /**
   * Optional text transformation to apply to the title and/or message.
   * For example, 'uppercase', 'lowercase', 'capitalize'.
   */
  caseTransform?: ChangeCaseTransform;
}
