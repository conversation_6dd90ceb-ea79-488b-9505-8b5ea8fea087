import { alpha, Box, useTheme } from '@mui/material';
import { type PlaceholderBoxProps } from '../../RadioButton.type';

/**
 * PlaceholderBox component provides a styled container with dashed borders and
 * diagonal stripe background pattern for placeholder content display.
 */
export const PlaceholderBox = ({
  children,
  height,
  disableHover,
  fixedHeight,
  flex,
  dark = false,
  sx,
  ...other
}: PlaceholderBoxProps) => {
  const theme = useTheme();

  const isDarkMode = theme.palette.mode === 'dark' || dark;

  const darkBackground = `repeating-linear-gradient(
    -55deg,
    ${alpha(theme.palette.common.black, 0.3)} 0px,
    ${alpha(theme.palette.common.black, 0.3)} 4px,
    ${alpha(theme.palette.neutral[900], 0.3)} 4px,
    ${alpha(theme.palette.neutral[900], 0.3)} 8px
  )`;

  const lightBackground = `repeating-linear-gradient(
    -55deg,
    ${alpha(theme.palette.common.white, 0.7)} 0px,
    ${alpha(theme.palette.common.white, 0.7)} 4px,
    ${alpha(theme.palette.neutral[100], 0.7)} 4px,
    ${alpha(theme.palette.neutral[100], 0.7)} 8px
  )`;

  return (
    <Box
      sx={{
        ...sx,
        flex: flex ? 1 : 0,
        borderRadius: theme.shape.borderRadius + 'px',
        borderStyle: 'dashed',
        borderWidth: 1,
        borderColor:
          theme.palette.mode === 'dark' ? theme.palette.neutral[800] : theme.palette.neutral[500],
        minHeight: height ?? 40,
        height: fixedHeight ?? '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: isDarkMode ? darkBackground : lightBackground,

        ...(!disableHover && {
          '&:hover': {
            borderColor: theme.palette.primary.main,
            boxShadow: theme.shadows[7],
          },
        }),
      }}
      {...other}
    >
      {children}
    </Box>
  );
};

export default PlaceholderBox;
