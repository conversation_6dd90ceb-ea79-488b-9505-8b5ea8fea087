import { type DropdownOption } from '@/types/form.type';
import { type SxProps, type Theme } from '@mui/material';
import { type ReactNode } from 'react';

interface SharedProps {
  name: string;
  title?: string;
  description?: string;
  selected?: string;
  disabled?: boolean;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export interface RadioButtonBasicProps extends SharedProps {
  display?: 'list' | 'row';
  items: DropdownOption[];
}

export interface RadioButtonCardProps extends SharedProps {
  radioIcon?: 'hidden' | 'left' | 'right';
  type?: 'card' | 'card-image';
  items: DropdownOption[];
}

export interface PlaceholderBoxProps {
  children?: ReactNode;
  height?: number;
  fixedHeight?: number;
  flex?: number;
  dark?: boolean;
  disableHover?: boolean;
  sx?: SxProps<Theme>;
}
