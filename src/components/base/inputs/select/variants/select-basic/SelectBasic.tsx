import { Typography } from '@/components/base/typography';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import { type SelectBasicProps } from '../../Select.type';

/**
 * SelectBasic component provides a basic dropdown select input with optional
 * title label and description helper text.
 */
export const SelectBasic = ({
  name,
  title = '',
  description = '',
  selected = '',
  items = [],
  disabled = false,
  onChange,
}: SelectBasicProps) => {
  return (
    <FormControl
      fullWidth
      sx={{ mt: 2 }}
    >
      {title && (
        <InputLabel
          id={`${name}-select-label`}
          sx={{ zIndex: 2 }}
        >
          <Typography caseTransform="sentenceCase">{title}</Typography>
        </InputLabel>
      )}
      <Select
        labelId={`${name}-select-label`}
        id={`${name}-select`}
        value={selected}
        label={title}
        disabled={disabled}
        onChange={onChange}
      >
        {items.length > 0 &&
          items.map((item) => (
            <MenuItem
              key={item.value}
              value={item.value}
            >
              <Typography caseTransform="sentenceCase">{item.label}</Typography>
            </MenuItem>
          ))}
      </Select>
      {description && <FormHelperText>{description}</FormHelperText>}
    </FormControl>
  );
};

export default SelectBasic;
