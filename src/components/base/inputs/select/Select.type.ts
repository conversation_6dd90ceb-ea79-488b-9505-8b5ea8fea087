import { type DropdownOption } from '@/types/form.type';
import { type SelectChangeEvent } from '@mui/material/Select';

export interface SelectBasicProps {
  name: string;
  title?: string;
  description?: string;
  selected?: string;
  disabled?: boolean;
  onChange?: (event: SelectChangeEvent<string>, child: React.ReactNode) => void;
  items: DropdownOption[];
}

export type SelectProps = SelectBasicProps;
