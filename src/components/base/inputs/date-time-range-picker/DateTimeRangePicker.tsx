import { StyledMultiInputDateTimeRangeField } from '@/components/base/inputs/date-time-range-picker/styles';
import { DateTimeRangePicker as MuiDateTimeRangePicker } from '@mui/x-date-pickers-pro/DateTimeRangePicker';
import { type DateTimeRangePickerProps } from './DateTimeRangePicker.type';

/**
 * A base component for selecting a range of date and time values.
 *
 * @param {Object} props - The component props.
 * @param {[Date | null, Date | null]} props.value - The current value of the date-time range picker.
 * @param {function} props.onChange - Callback function to handle changes to the selected date-time range.
 * @param {string} [props.format='dd/MM/yyyy HH:mm:ss'] - The format string for displaying the date and time.
 * @param {boolean} [props.ampm=false] - Indicates whether to display AM/PM in the time picker.
 * @param {boolean} [props.multiInput=false] - Indicates whether to use multiple input fields for the date-time range.
 * @param {Object} [props.slots] - Additional slots for the date-time range picker.
 * @param {Object} [props.localeText] - Custom locale text for the date-time range picker.
 * @param {string} [props.views=['day', 'hours','minutes','seconds']] - The views to display in the date-time range picker.
 * @returns {JSX.Element} A JSX element representing the date-time range picker.
 */
export const DateTimeRangePicker = ({
  value,
  onChange,
  ampm = false,
  format = ampm ? 'dd/MM/yyyy hh:mm:ss a' : 'dd/MM/yyyy HH:mm:ss',
  views = ['day', 'hours', 'minutes', 'seconds'],
  localeText: customLocaleText,
  multiInput = false,
  slots,
  ...otherProps
}: DateTimeRangePickerProps) => {
  const finalSlots = multiInput ? { field: StyledMultiInputDateTimeRangeField, ...slots } : slots;

  return (
    <MuiDateTimeRangePicker
      value={value}
      onChange={onChange}
      format={format}
      ampm={ampm}
      views={views}
      localeText={customLocaleText}
      sx={{ width: '100%' }}
      slots={finalSlots}
      slotProps={{ textField: { fullWidth: true } }}
      {...otherProps}
    />
  );
};

export default DateTimeRangePicker;
