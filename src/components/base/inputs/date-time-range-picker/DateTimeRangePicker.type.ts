import type { DateTimeRangePickerProps as MuiDateTimeRangePickerProps } from '@mui/x-date-pickers-pro/DateTimeRangePicker';

/**
 * Props for the DateTimeRangePicker component.
 * Extends MUI's DateTimeRangePickerProps to inherit all capabilities (e.g., min/max, timezone, slotProps).
 * Overrides `value` and `onChange` to keep a simple tuple signature across the app.
 */
export interface DateTimeRangePickerProps
  extends Omit<MuiDateTimeRangePickerProps, 'value' | 'onChange'> {
  /**
   * The current value of the date-time range picker, represented as a tuple of two dates.
   * Each date can be a Date object or null.
   */
  value: [Date | null, Date | null];
  /**
   * Callback function that is called when the date-time range changes.
   * @param range - A tuple containing the new start and end dates, each of which can be a Date object or null.
   */
  onChange: (range: [Date | null, Date | null]) => void;

  /**
   * Indicates whether the date-time range picker should use multiple input fields.
   * @default false
   *
   * When set to true, the picker will display separate input fields for the start and end dates.
   * When set to false, a single input field will be used for the entire date-time range.
   */
  multiInput: boolean;
}
