import { Checkbox, Input, Radio, styled } from '@mui/material';

/**
 * CSS styles function that creates visually hidden elements for accessibility.
 * Elements remain accessible to screen readers while being invisible to sighted users.
 */
const visuallyHidden = () => `
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
  bottom: 0;
  left: 0;
`;

/**
 * Styled MUI Input component with visually hidden styles applied.
 */
export const VisuallyHiddenInput = styled(Input)(visuallyHidden);

/**
 * Styled native HTML input element with visually hidden styles applied.
 */
export const VisuallyHiddenInputNative = styled('input')(visuallyHidden);

/**
 * Styled MUI Radio component with visually hidden styles applied.
 */
export const VisuallyHiddenRadio = styled(Radio)(visuallyHidden);

/**
 * Styled MUI Checkbox component with visually hidden styles applied.
 */
export const VisuallyHiddenCheckbox = styled(Checkbox)(visuallyHidden);
