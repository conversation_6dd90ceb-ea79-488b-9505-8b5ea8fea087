import { type ReactElement } from 'react';
import {
  VisuallyHiddenCheckbox,
  VisuallyHiddenInput,
  VisuallyHiddenInputNative,
  VisuallyHiddenRadio,
} from './styles/VisuallyHidden.style';
import type { VisuallyHiddenProps } from './VisuallyHidden.type';

/**
 * VisuallyHidden component renders form inputs that are accessible to screen readers
 * but visually hidden from sighted users, supporting multiple input types.
 */
export const VisuallyHidden = ({ inputType, ...props }: VisuallyHiddenProps) => {
  let Component: ReactElement;

  switch (inputType) {
    case 'input':
      Component = <VisuallyHiddenInput {...props} />;
      break;
    case 'radio':
      Component = <VisuallyHiddenRadio {...props} />;
      break;
    case 'checkbox':
      Component = <VisuallyHiddenCheckbox {...props} />;
      break;
    default:
      Component = <VisuallyHiddenInputNative {...props} />;
      break;
  }

  return <>{Component}</>;
};

export default VisuallyHidden;
