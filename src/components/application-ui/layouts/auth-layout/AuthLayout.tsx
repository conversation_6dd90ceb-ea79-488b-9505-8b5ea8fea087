import { FormAlert } from '@/components/base/feedback/alert/FormAlert';
import { usePageAlertStore } from '@/contexts/app';
import { Box, Container, Grid, Paper } from '@mui/material';
import { useEffect, type JSX } from 'react';
import type { AuthLayoutProps } from './AuthLayout.type';

export const AuthLayout = ({
  children,
  maxWidth = 'sm',
}: Readonly<AuthLayoutProps>): JSX.Element => {
  const { title, message, severity, display, closeAlert } = usePageAlertStore();
  useEffect(() => {
    // Clear alerts whenever a new auth page is rendered
    closeAlert();
  }, [closeAlert]);

  return (
    <Grid
      container
      component="main" // real <main> for a11y
      minHeight="100vh" // reliable full-height
      width="100%"
    >
      <Grid
        component={Paper}
        elevation={6}
        square
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%', // ensure Paper spans width
        }}
        p={1}
      >
        <Box
          border={1}
          borderColor="divider"
          pt={{ xs: 2, sm: 8 }}
          pb={{ xs: 2, sm: 8 }}
          px={{ xs: 2, sm: 10 }}
        >
          {/* Alert message */}
          <Container maxWidth={maxWidth}>
            {display === true && (
              <Box pb={2}>
                <FormAlert
                  severity={severity}
                  title={title}
                  message={message}
                  onClose={closeAlert}
                />
              </Box>
            )}
          </Container>
          {children}
        </Box>
      </Grid>
    </Grid>
  );
};

export default AuthLayout;
