import { UnsavedChangesDialog } from '@/components/application-ui/alert-dialog';
import { PageHeading } from '@/components/base/headings/page-heading';
import { Button } from '@/components/base/inputs/button';
import { useCustomization } from '@/hooks/ui/use-customization.hook';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { type FormAction } from '@/types/form.type';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Divider from '@mui/material/Divider';
import Stack from '@mui/material/Stack';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { type FormLayoutProps } from './FormLayout.type';

/**
 * FormLayout provides a standardized layout for forms with header, submit/reset buttons, and unsaved changes handling.
 */
export const FormLayout = ({
  FormComponent,
  title,
  description,
  showSubmit,
  submitLabel,
  showReset,
  resetLabel,
  unsaved,
}: FormLayoutProps) => {
  const { stretch } = useCustomization();
  const { t } = useTranslation();

  const translatedTitle = t(title);
  const translatedDescription = description ? t(description) : undefined;

  usePageTitle(translatedTitle);

  const formRef = useRef<FormAction>({});
  const [isDirty, setIsDirty] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const disableSubmit = isLoading || !isDirty || !formRef.current.submit;
  const disableReset = isLoading || !isDirty || !formRef.current.reset;

  return (
    <>
      <PageHeading
        title={translatedTitle}
        description={translatedDescription}
        actions={
          <Stack
            direction={{ xs: 'row' }}
            spacing={1.5}
            whiteSpace="nowrap"
            alignItems="center"
            width={{ xs: '100%', sm: '100%', md: 'auto' }}
          >
            {showReset && (
              <Button
                variant="outlined"
                fullWidth
                disabled={disableReset}
                onClick={formRef.current?.reset}
              >
                {resetLabel ?? t('common.label.reset')}
              </Button>
            )}
            {showSubmit && (
              <Button
                variant="contained"
                loading={isLoading}
                fullWidth
                disabled={disableSubmit}
                onClick={formRef.current?.submit}
              >
                {submitLabel ?? t('common.label.save')}
              </Button>
            )}
          </Stack>
        }
      />
      <Divider />
      <Box py={{ xs: 2, sm: 3 }}>
        <Container maxWidth={stretch ? false : 'xl'}>
          <FormComponent
            setForm={({ submit, reset }) => {
              formRef.current.submit = submit;
              formRef.current.reset = reset;
            }}
            setDirty={(isDirty) => setIsDirty(isDirty)}
            setLoading={(isLoading) => setIsLoading(isLoading)}
          />
          {unsaved && <UnsavedChangesDialog enabled={isDirty} />}
        </Container>
      </Box>
    </>
  );
};

export default FormLayout;
