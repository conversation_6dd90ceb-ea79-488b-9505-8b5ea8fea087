import { type PaletteColor<PERSON><PERSON> } from '@/theme/colors';

export type ButtonProps = {
  label: string;
  colour: PaletteColorKey;
};

export interface ConfirmationDialogProps {
  display: boolean;
  title: string;
  description?: string;
  showCancel?: boolean;
  cancelLabel?: string;
  cancelColour?: PaletteColorKey;
  cancelAction?: () => void;
  showConfirm?: boolean;
  confirmLabel?: string;
  confirmColour?: PaletteColorKey;
  confirmAction?: () => void;
  progressBar?: boolean;
}
