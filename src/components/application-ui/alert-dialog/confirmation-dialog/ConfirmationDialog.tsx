import { AlertDialog } from '@/components/base/feedback/dialog';
import { Button } from '@/components/base/inputs/button';
import { useDialog } from '@/hooks/ui/use-dialog.hook';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { type ConfirmationDialogProps } from './ConfirmationDialog.type';

/**
 * A confirmation dialog component with customizable title, description, and action buttons.
 */
export const ConfirmationDialog = ({
  display,
  title,
  description,
  showCancel,
  cancelLabel = 'common.label.cancel',
  cancelColour = 'secondary',
  cancelAction,
  showConfirm,
  confirmLabel = 'common.label.confirm',
  confirmColour = 'primary',
  confirmAction,
  progressBar,
}: ConfirmationDialogProps) => {
  const { t } = useTranslation();
  const { open, handleOpen, handleClose } = useDialog();

  const onClose = () => {
    handleClose();
    cancelAction?.();
  };

  const onConfirm = () => {
    handleClose();
    confirmAction?.();
  };

  useEffect(() => {
    if (display) handleOpen();
  }, [display, handleOpen]);

  return (
    <AlertDialog
      open={open}
      onClose={onClose}
      title={title}
      message={description}
      actions={
        <>
          {showCancel && (
            <Button
              variant="text"
              color={cancelColour}
              onClick={onClose}
            >
              {t(cancelLabel)}
            </Button>
          )}
          {showConfirm && (
            <Button
              {...(progressBar && { 'data-nprogress': 'true' })}
              variant="text"
              color={confirmColour}
              onClick={onConfirm}
              autoFocus
            >
              {t(confirmLabel)}
            </Button>
          )}
        </>
      }
    />
  );
};

export default ConfirmationDialog;
