import { FormAlert } from '@/components/base/feedback/alert';
import { usePageAlertStore } from '@/contexts/app';
import { usePathname } from '@/hooks/navigation/use-pathname.hook';
// import useScrollDirection from '@/hooks/ui/use-scroll-direction.hook';\
import {
  // HEADER_HEIGHT,
  SIDEBAR_WIDTH,
} from '@/theme/utils';
import Box from '@mui/material/Box';
import { useEffect, useRef } from 'react';
import { type PageAlertProps } from './PageAlert.type';

/**
 * PageAlert component displays alert messages at the top or bottom of the page
 * with configurable placement and automatic dismissal on route changes.
 */
export const PageAlert = ({ placement }: PageAlertProps) => {
  const { title, message, severity, display, autoCloseInMs, closeAlert } = usePageAlertStore();
  const pathname = usePathname();
  const prevPathnameRef = useRef(pathname);
  // const scroll = useScrollDirection();\

  const placementStyle =
    placement === 'bottom'
      ? {
          position: 'fixed',
          borderRadius: 0,
          margin: 0,
          bottom: 0,
          zIndex: 9999,
          left: { xs: 0, lg: `${SIDEBAR_WIDTH}px` },
          right: 0,
        }
      : {
          // Uncomment if need to be fixed to top
          // position: 'fixed',
          // borderRadius: 0,
          // margin: 0,
          // zIndex: 9999,
          // left: { xs: 0, lg: `${SIDEBAR_WIDTH}px` },
          // right: 0,
          // top: scroll === 'down' ? HEADER_HEIGHT : HEADER_HEIGHT * 1.5,
          // transition: theme.transitions.create(['height']),
        };

  // Close alert when pathname change
  useEffect(() => {
    if (pathname !== prevPathnameRef.current) {
      if (display) closeAlert();
      prevPathnameRef.current = pathname;
    }
  }, [pathname, display, closeAlert]);

  // Auto close alert after timeout
  useEffect(() => {
    let timer: ReturnType<typeof setTimeout> | undefined;
    if (display && autoCloseInMs > 0) {
      timer = setTimeout(() => closeAlert(), autoCloseInMs);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [display, autoCloseInMs, closeAlert]);

  return (
    <>
      {display === true && (
        <Box
          data-testid={`${placement}-page-alert`}
          sx={{ ...placementStyle }}
        >
          <FormAlert
            severity={severity}
            title={title}
            message={message}
            onClose={closeAlert}
          />
        </Box>
      )}
    </>
  );
};

export default PageAlert;
