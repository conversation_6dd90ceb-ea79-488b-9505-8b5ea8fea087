import { RadioButtonCard } from '@/components/base/inputs/radio-button';
import { SelectBasic } from '@/components/base/inputs/select';
import type { SelectChangeEvent } from '@mui/material';
import type { ChangeEvent } from 'react';
import { useTranslation } from 'react-i18next';
import type { FormFieldProps } from './FormField.type';

export const FormField = <TFormData,>({
  form,
  item,
  itemLists,
  disabled,
}: FormFieldProps<TFormData>) => {
  const { t } = useTranslation();

  return (
    <form.Field name={item.name}>
      {(field) => {
        const commonProps = {
          name: item.name,
          disabled: disabled,
          selected: disabled ? '' : field.state.value?.toString(),
          items: itemLists ?? [],

          ...(item.title && { title: t(item.title) }),
          ...(item.description && { description: t(item.description) }),
        };
        return (
          <>
            {item.type === 'select' && (
              <SelectBasic
                {...commonProps}
                onChange={(e: SelectChangeEvent<string>) =>
                  field.handleChange(e.target.value as never)
                }
              />
            )}
            {item.type === 'radio-button-card-image' && (
              <RadioButtonCard
                {...commonProps}
                type="card-image"
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  field.handleChange(e.target.value as never)
                }
              />
            )}
          </>
        );
      }}
    </form.Field>
  );
};

export default FormField;
