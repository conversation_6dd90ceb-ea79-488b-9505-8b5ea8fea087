import { Button } from '@/components/base/inputs/button';
import { Stack } from '@mui/material';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { type FilterActionButtonsProps } from './FilterActionButtons.type';

/**
 * FilterActionButtons component displays the search and clear buttons for filter panels
 *
 * @example
 * ```tsx
 * <FilterActionButtons
 *   onSearch={handleApplyFilters}
 *   onClear={handleResetFilters}
 * />
 * ```
 */
export const FilterActionButtons = memo(
  ({
    onSearch,
    onClear,
    searchButtonText,
    clearButtonText,
    searchButtonColor = 'primary',
    showClearButton = true,
    clearButtonTestId,
    searchButtonTestId,
  }: FilterActionButtonsProps) => {
    const { t } = useTranslation();

    return (
      <Stack
        p={{ xs: 2, sm: 3 }}
        spacing={1}
      >
        <Button
          color={searchButtonColor}
          customStyle="soft"
          variant="outlined"
          fullWidth
          onClick={onSearch}
          {...(searchButtonTestId ? { 'data-testid': searchButtonTestId } : {})}
        >
          {searchButtonText ?? t('common.label.search')}
        </Button>

        {showClearButton && (
          <Stack
            direction="row"
            sx={{
              justifyContent: 'flex-end',
              alignItems: 'center',
            }}
          >
            <Button
              color="inherit"
              variant="text"
              size="small"
              onClick={onClear}
              {...(searchButtonTestId ? { 'data-testid': clearButtonTestId } : {})}
            >
              {clearButtonText ?? t('common.action.clearAll')}
            </Button>
          </Stack>
        )}
      </Stack>
    );
  }
);

export default FilterActionButtons;
