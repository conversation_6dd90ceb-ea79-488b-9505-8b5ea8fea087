import type { ChangeCaseTransform } from '@/components/base/typography/Typography.type';

/**
 * Props for the FilterCheckbox component
 */
export interface FilterCheckboxProps {
  /**
   * The label of the filter section
   */
  label: string;

  /**
   * The filter options to display
   */
  options: Array<{
    value: string;
    labelKey: string;
    caseTransform?: ChangeCaseTransform;
    testId?: string;
  }>;

  /**
   * The currently selected values
   */
  selectedValues: string[];

  /**
   * Callback fired when a value is selected or deselected
   */
  onChange: (value: string) => void;

  /**
   * Whether the accordion should be expanded by default
   * @default true
   */
  defaultExpanded?: boolean;

  /**
   * Test ID for the accordion
   */
  accordionTestId?: string;

  /**
   * Test ID for the text input (optional)
   */
  inputTestId?: string;
}
