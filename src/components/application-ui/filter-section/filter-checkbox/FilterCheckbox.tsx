import { Accordion } from '@/components/base/surface/accordion';
import { Typography } from '@/components/base/typography';
import transformText from '@/utils/text-transform.util';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  AccordionDetails,
  AccordionSummary,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Stack,
} from '@mui/material';
import { memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { type FilterCheckboxProps } from './FilterCheckbox.type';

/**
 * FilterCheckbox component displays a collapsible section with checkboxes for filtering
 *
 * @example
 * ```tsx
 * <FilterCheckbox
 *   label="status"
 *   options={[
 *     { value: 'active', labelKey: 'active' },
 *     { value: 'inactive', labelKey: 'inactive', caseTransform: 'sentenceCase' }
 *   ]}
 *   selectedValues={['active']}
 *   onChange={handleStatusChange}
 * />
 * ```
 */
export const FilterCheckbox = memo(
  ({
    label,
    options,
    selectedValues,
    onChange,
    defaultExpanded = true,
    accordionTestId,
    inputTestId,
  }: FilterCheckboxProps) => {
    const { t } = useTranslation();

    const handleToggle = useCallback((v: string) => () => onChange(v), [onChange]);

    return (
      <Accordion
        defaultExpanded={defaultExpanded}
        customStyle="minimal"
        data-testid={accordionTestId}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography
            variant="h5"
            caseTransform="sentenceCase"
          >
            {t(label)}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Stack
            px={{ xs: 2, sm: 3 }}
            pb={2}
          >
            <FormGroup data-testid={inputTestId}>
              {options.map((option) => (
                <FormControlLabel
                  key={option.value}
                  control={
                    <Checkbox
                      checked={selectedValues.includes(option.value)}
                      onChange={handleToggle(option.value)}
                      data-testid={option.testId}
                    />
                  }
                  label={transformText(t(option.labelKey), option.caseTransform ?? 'sentenceCase')}
                />
              ))}
            </FormGroup>
          </Stack>
        </AccordionDetails>
      </Accordion>
    );
  }
);

export default FilterCheckbox;
