import { Accordion } from '@/components/base/surface/accordion';
import { Typography } from '@/components/base/typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { AccordionDetails, AccordionSummary, OutlinedInput, Stack } from '@mui/material';
import { forwardRef, memo, useId } from 'react';
import { useTranslation } from 'react-i18next';
import type { FilterInputProps } from './FilterInput.type';

/**
 * FilterInput component displays a collapsible section with a single input field for filtering
 *
 * @example
 * ```tsx
 * <FilterInput
 *   label="myField"
 *   value={value}
 *   onChange={handleValueChange}
 * />
 * ```
 */
export const FilterInput = memo(
  forwardRef<HTMLInputElement, FilterInputProps>(
    (
      { label, defaultExpanded = true, inputTestId, accordionTestId, ...props }: FilterInputProps,
      ref
    ) => {
      const { t } = useTranslation();
      const inputId = useId();

      return (
        <Accordion
          defaultExpanded={defaultExpanded}
          customStyle="minimal"
          data-testid={accordionTestId}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography
              variant="h5"
              caseTransform="sentenceCase"
            >
              {t(label)}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Stack
              px={{ xs: 2, sm: 3 }}
              pb={2}
            >
              <OutlinedInput
                id={inputId}
                aria-label={t(label)}
                data-testid={inputTestId}
                {...props}
                ref={ref}
              />
            </Stack>
          </AccordionDetails>
        </Accordion>
      );
    }
  )
);

export default FilterInput;
