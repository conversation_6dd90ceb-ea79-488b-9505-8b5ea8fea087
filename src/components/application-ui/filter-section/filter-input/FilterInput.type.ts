import type { OutlinedInputProps } from '@mui/material';

/**
 * Props for the FilterInput component
 */
export interface FilterInputProps extends OutlinedInputProps {
  /**
   * The label of the filter section
   */
  label: string;

  /**
   * Whether the accordion should be expanded by default
   * @default true
   */
  defaultExpanded?: boolean;

  /**
   * Test ID for the accordion (optional)
   */
  accordionTestId?: string;

  /**
   * Test ID for the text input (optional)
   */
  inputTestId?: string;
}
