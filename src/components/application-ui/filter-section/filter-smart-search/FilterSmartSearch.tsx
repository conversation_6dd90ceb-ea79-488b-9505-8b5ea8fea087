import { Button } from '@/components/base/inputs/button';
import { Accordion } from '@/components/base/surface/accordion';
import { Typography } from '@/components/base/typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SearchIcon from '@mui/icons-material/Search';
import {
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Box,
  TextField,
  type Theme,
} from '@mui/material';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { FilterSmartSearchProps } from './FilterSmartSearch.type';

/**
 * A component that provides a smart search filter interface, allowing users to add and manage filters
 * based on predefined options. It supports input parsing, filter addition, and deletion.
 *
 * @param label - The label for the filter section, used for display purposes.
 * @param filterOptions - An array of available filter options, each with a key and label.
 * @param filters - The current list of active filters, each represented by a key-value pair.
 * @param onFiltersChange - A callback function that is triggered when the filters change.
 * @param defaultExpanded - A boolean indicating whether the accordion should be expanded by default.
 * @returns A JSX element representing the filter smart search component.
 */
export const FilterSmartSearch = ({
  label,
  filterOptions,
  filters,
  placeholder = 'common.label.smartFilterPlaceholder',
  onFiltersChange,
  defaultExpanded = true,
  ...otherProps
}: FilterSmartSearchProps & { defaultExpanded?: boolean }) => {
  const { t } = useTranslation();

  const [inputValue, setInputValue] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  /**
   * Parses input into { key, value } form by splitting on colon.
   */
  const parsedInput = useMemo(() => {
    const [rawKey, ...rest] = inputValue.split(':');
    return {
      key: rawKey?.trim() ?? '',
      value: rest.join(':').trim(),
    };
  }, [inputValue]);

  const isTypingField = !inputValue.includes(':');
  const isValidField = filterOptions.some((f) => f.key === parsedInput.key);

  /**
   * Handles changes in the autocomplete component.
   * This manages both the filter selection and input changes.
   */
  const handleChange = (_: unknown, newValue: string[] | string | null, reason: string) => {
    if (reason === 'removeOption' || reason === 'clear') {
      handleFilterRemoval(newValue);
    }
    // Don't handle selectOption here - let onInputChange handle it instead
    // This prevents immediate chip creation when selecting field options
  };

  /**
   * Handles filter removal when chips are deleted
   */
  const handleFilterRemoval = (newValue: string[] | string | null) => {
    if (Array.isArray(newValue)) {
      const newFilters = newValue.map((val) => {
        const [key, ...rest] = val.split(':');
        return { key: (key ?? '').trim(), value: rest.join(':').trim() };
      });
      onFiltersChange(newFilters);
    } else if (newValue === null) {
      onFiltersChange([]);
    }
  };

  /**
   * Creates a new filter from the current input
   */
  const createFilterFromInput = () => {
    const { key, value } = parsedInput;
    if (isValidField && value.trim()) {
      const exists = filters.some((f) => f.key === key && f.value === value);
      if (!exists) {
        onFiltersChange([...filters, { key, value }]);
        setInputValue('');
        return true;
      }
    }
    return false;
  };

  /**
   * Handles input value changes in the autocomplete field.
   */
  const handleInputChange = (_: unknown, newInputValue: string, reason: string) => {
    if (reason === 'input') {
      setInputValue(newInputValue);
    } else if (reason === 'clear') {
      setInputValue('');
    }
    // selectOption is now handled in onChange instead
  };

  /**
   * Handles keyboard events like Enter (add) and Backspace (delete last).
   */
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' && inputValue?.includes(':')) {
      e.preventDefault();
      createFilterFromInput();
    }

    if (e.key === 'Backspace' && inputValue === '' && filters.length > 0) {
      e.preventDefault();
      const newFilters = [...filters];
      newFilters.pop();
      onFiltersChange(newFilters);
    }
  };

  /**
   * Toggles the visibility of the filters section.
   */
  const handleToggleFilters = () => {
    setShowFilters((prev) => !prev);
  };

  return (
    <Accordion
      defaultExpanded={defaultExpanded}
      customStyle="minimal"
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography
          variant="h5"
          caseTransform="sentenceCase"
        >
          {t(label)}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box
          px={{ xs: 2, sm: 3 }}
          pb={2}
        >
          <Autocomplete
            multiple
            freeSolo
            value={filters.map((f) => `${f.key}:${f.value}`)}
            inputValue={inputValue}
            {...otherProps}
            onChange={(event, newValue, reason) => {
              if (reason === 'createOption' && inputValue.includes(':')) {
                createFilterFromInput();
                return;
              }

              if (reason === 'selectOption') {
                if (isTypingField && Array.isArray(newValue)) {
                  const selected = newValue[newValue.length - 1];
                  const matched = filterOptions.find(
                    (opt) => opt.label.toLowerCase() === selected?.toLowerCase()
                  );
                  if (matched) {
                    setInputValue(`${matched.key}:`);
                  }
                  return;
                }
                return;
              }

              handleChange(event, newValue, reason);
            }}
            onInputChange={handleInputChange}
            onKeyDown={handleKeyDown}
            options={isTypingField ? filterOptions.map((opt) => opt.label) : []}
            filterOptions={(options, { inputValue: filterInput }) => {
              if (!isTypingField) return options;
              return options.filter((option) =>
                option.toLowerCase().includes(filterInput.toLowerCase())
              );
            }}
            selectOnFocus
            clearOnBlur={false}
            handleHomeEndKeys
            blurOnSelect={false}
            autoHighlight
            renderOption={(props, option) => {
              const { key, ...rest } = props;
              return (
                <li
                  key={key}
                  {...rest}
                >
                  <SearchIcon
                    fontSize="small"
                    sx={{ mr: 1, color: 'text.secondary' }}
                  />
                  {option}
                </li>
              );
            }}
            slotProps={{
              chip: {
                size: 'small',
                sx: {
                  maxWidth: 150,
                },
              },
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder={filters.length === 0 ? t(placeholder) : ''}
                aria-label={t('common.aria.search')}
                data-testid="search-input"
                helperText={inputValue ? t('common.sentence.smartSearchFilterHelper') : ''}
              />
            )}
            sx={{ width: '100%' }}
          />

          {filters.length > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
              <Button
                onClick={handleToggleFilters}
                size="small"
                variant="text"
                data-testid="toggle-filters-button"
                aria-label="toggle-filters-button"
              >
                {showFilters ? t('common.action.hideFilters') : t('common.action.showFilters')}
              </Button>
            </Box>
          )}

          {showFilters && filters.length > 0 && (
            <Box
              sx={{
                mt: 1,
                background: (theme: Theme) =>
                  theme.palette.mode === 'light'
                    ? theme.palette.neutral?.[50] // Light mode background
                    : theme.palette.neutral?.[900], // Dark mode background
                px: 1,
                py: 0.5,
                borderRadius: 1,
              }}
            >
              <Typography
                variant="body1"
                color="textSecondary"
                sx={{
                  mb: 0.5,
                }}
              >
                {t('common.label.activeFilters')}:
              </Typography>
              {filters.map((f) => (
                <Typography
                  key={`${f.key}:${f.value}`}
                  variant="body2"
                  color="textSecondary"
                  sx={{
                    mt: 0.5,
                    wordBreak: 'break-word',
                  }}
                >
                  {f.key}:{f.value}
                </Typography>
              ))}
            </Box>
          )}
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};

export default FilterSmartSearch;
