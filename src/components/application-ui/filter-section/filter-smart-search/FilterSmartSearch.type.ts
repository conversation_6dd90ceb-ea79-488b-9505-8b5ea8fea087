import type { AutocompleteProps } from '@mui/material/Autocomplete';

/**
 * Props for the `FilterSmartSearch` component.
 *
 * Inherits all props from MUI's `AutocompleteProps` configured for:
 * - Option type: `string`
 * - Multiple selection: `true`
 * - Disable clearable: `false` (clearable allowed)
 * - Free solo: `true` (custom values allowed)
 *
 * Omits `options` and `filterOptions` from `AutocompleteProps`
 * to use the component's own strongly typed `filterOptions` prop.
 */
export interface FilterSmartSearchProps
  extends Omit<AutocompleteProps<string, true, false, true>, 'options' | 'filterOptions'> {
  /**
   * Display label for the smart search input, shown as the field label.
   */
  label: string;

  /**
   * List of available filter options that can be selected or typed.
   * Each option contains a `key` and a `label`.
   */
  filterOptions: FilterOption[];

  /**
   * Optional placeholder text to display inside the input when it's empty.
   */
  placeholder?: string;

  /**
   * Array of currently applied filters.
   * Each filter contains a `key` (matches a filter option) and a `value` (input text/value).
   */
  filters: Filter[];

  /**
   * <PERSON><PERSON> fired whenever the applied filter list changes.
   *
   * @param filters - The updated list of filters after the change.
   */
  onFiltersChange: (filters: Filter[]) => void;
}

/**
 * Represents a selectable filter option.
 */
export interface FilterOption {
  /**
   * Internal key used for matching and filtering.
   */
  key: string;

  /**
   * Human-readable label shown to the user in the dropdown and chips.
   */
  label: string;
}

/**
 * Represents a filter that has been applied by the user.
 */
export interface Filter {
  /**
   * Filter key (should correspond to a `FilterOption.key`).
   */
  key: string;

  /**
   * The value entered for this filter.
   */
  value: string;
}
