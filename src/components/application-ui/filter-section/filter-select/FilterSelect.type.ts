import type { OutlinedSelectProps } from '@mui/material';

/**
 * Props for the FilterSelect component
 */
export interface FilterSelectProps extends OutlinedSelectProps {
  /**
   * The label of the filter section
   */
  label: string;

  /**
   * The menu items of the filter section
   */
  menuItems: { value: string; label: string; testId?: string }[];

  /**
   * Whether the accordion should be expanded by default
   * @default true
   */
  defaultExpanded?: boolean;

  /**
   * Test ID for the accordion
   */
  accordionTestId?: string;

  /**
   * Test ID for the text input (optional)
   */
  inputTestId?: string;
}
