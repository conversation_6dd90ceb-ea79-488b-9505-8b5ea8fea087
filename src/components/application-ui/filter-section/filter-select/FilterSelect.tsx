import { Accordion } from '@/components/base/surface/accordion';
import { Typography } from '@/components/base/typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  AccordionDetails,
  AccordionSummary,
  FormControl,
  MenuItem,
  Select,
  Stack,
} from '@mui/material';
import { forwardRef, memo, useId } from 'react';
import { useTranslation } from 'react-i18next';
import type { FilterSelectProps } from './FilterSelect.type';

/**
 * FilterSelect component displays a collapsible section with a select dropdown for filtering
 *
 * @example
 * ```tsx
 * <FilterSelect
 *   label="status"
 *   menuItems={[
 *     { value: 'active', labelKey: 'active' },
 *     { value: 'inactive', labelKey: 'inactive', caseTransform: 'sentenceCase' }
 *   ]}
 *   value={selectValue}
 *   onChange={handleSelectChange}
 * />
 * ```
 */
export const FilterSelect = memo(
  forwardRef<HTMLElement, Readonly<FilterSelectProps>>(
    ({ label, menuItems, defaultExpanded = true, accordionTestId, inputTestId, ...props }, ref) => {
      const { t } = useTranslation();

      const selectId = useId();

      return (
        <Accordion
          defaultExpanded={defaultExpanded}
          customStyle="minimal"
          data-testid={accordionTestId}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography
              variant="h5"
              caseTransform="sentenceCase"
            >
              {t(label)}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Stack
              px={{ xs: 2, sm: 3 }}
              pb={2}
            >
              <FormControl fullWidth>
                <Select
                  {...props}
                  id={selectId}
                  data-testid={inputTestId}
                  ref={ref}
                >
                  {menuItems.map((menuItem) => (
                    <MenuItem
                      key={menuItem.value}
                      value={menuItem.value}
                      data-testid={menuItem.testId}
                    >
                      {t(menuItem.label)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Stack>
          </AccordionDetails>
        </Accordion>
      );
    }
  )
);

export default FilterSelect;
