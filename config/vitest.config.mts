import path from 'node:path';
import { fileURLToPath } from 'node:url';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';
import { defineConfig } from 'vitest/config';

const dirname =
  typeof __dirname !== 'undefined' ? __dirname : path.dirname(fileURLToPath(import.meta.url));

// Get the project root directory (one level up from config directory)
const projectRoot = path.resolve(dirname, '..');

export default defineConfig({
  plugins: [tsconfigPaths(), react()],
  root: projectRoot,
  test: {
    include: ['test/**/*.test.{ts,tsx}'],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.next/**',
      '**/coverage/**',
      'src/**/constants/**',
      'src/**/types/**',
      'src/**/router/**',
      'src/**/styles/**',
      'src/**/mocks/**',
      'src/**/*.style.{ts,tsx}',
      'src/**/*.styles.{ts,tsx}',
      'src/**/*.mock.{ts,tsx}',
    ],
    environment: 'jsdom',
    globals: true,
    typecheck: {
      enabled: true,
      tsconfig: './tsconfig.json',
    },
    server: {
      deps: {
        inline: ['@mui/x-data-grid'],
      },
    },
    setupFiles: ['config/vitest.setup.ts'],
    coverage: {
      enabled: true,
      include: ['src/**/*.{ts,tsx}'],
      exclude: [
        '**/*.stories.*',
        '**/*.type.*',
        '**/*.config.*',
        '**/*.constant.*',
        '**/*.style.*',
        '**/*.mock.*',
        '**/index.{ts,tsx,js,jsx}',
        '**/*.d.ts',
        '**/*.error.ts',
        '**/theme/**',
      ],
      provider: 'v8',
      reporter: ['text', 'html', 'lcov'],
      reportsDirectory: './coverage',
      // Enable the following line to enforce stricter code coverage thresholds when committing code
      // thresholds: {
      //   statements: 80,
      //   branches: 80,
      //   functions: 80,
      //   lines: 80,
      // },
    },
    testTimeout: 60000,
  },
});
