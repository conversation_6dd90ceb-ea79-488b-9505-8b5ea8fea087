import { beforeAll } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { setProjectAnnotations } from '@storybook/nextjs-vite';
import * as projectAnnotations from '../.storybook/preview';

// This is an important step to apply the right configuration when testing your stories.
// More info at: https://storybook.js.org/docs/api/portable-stories/portable-stories-vitest#setprojectannotations
const annotations = setProjectAnnotations([projectAnnotations]);

// Run Storybook's beforeAll hook
beforeAll(annotations.beforeAll);
